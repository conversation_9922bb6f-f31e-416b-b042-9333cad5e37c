{"version": 3, "sources": ["../../@stagewise-plugins/vue/dist/index.es.js"], "sourcesContent": ["import { jsxs, jsx } from \"@stagewise/toolbar/plugin-ui/jsx-runtime\";\nfunction Vue<PERSON>ogo() {\n  return /* @__PURE__ */ jsxs(\n    \"svg\",\n    {\n      xmlns: \"http://www.w3.org/2000/svg\",\n      viewBox: \"0 0 261.76 226.69\",\n      fill: \"currentColor\",\n      children: [\n        /* @__PURE__ */ jsx(\"title\", { children: \"Vue Logo\" }),\n        /* @__PURE__ */ jsxs(\n          \"g\",\n          {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            transform: \"matrix(1.3333 0 0 -1.3333 -76.311 313.34)\",\n            children: [\n              /* @__PURE__ */ jsx(\"g\", { transform: \"translate(178.06 235.01)\", fillOpacity: \"0.5\", children: /* @__PURE__ */ jsx(\"path\", { d: \"m0 0-22.669-39.264-22.669 39.264h-75.491l98.16-170.02 98.16 170.02z\" }) }),\n              /* @__PURE__ */ jsx(\"g\", { transform: \"translate(178.06 235.01)\", \"fill-opacity\": \"1\", children: /* @__PURE__ */ jsx(\"path\", { d: \"m0 0-22.669-39.264-22.669 39.264h-36.227l58.896-102.01 58.896 102.01z\" }) })\n            ]\n          }\n        )\n      ]\n    }\n  );\n}\nlet hasWarnedNoVue = false;\nfunction getVueComponentHierarchy(element) {\n  var _a;\n  if (!element) {\n    return null;\n  }\n  const components = [];\n  const maxComponents = 3;\n  let currentElement = element;\n  while (currentElement && components.length < maxComponents) {\n    const parentComponent = currentElement.__vueParentComponent;\n    if ((_a = parentComponent == null ? void 0 : parentComponent.type) == null ? void 0 : _a.__name) {\n      const componentName = parentComponent.type.__name;\n      if (!components.some((c) => c.name === componentName)) {\n        components.push({ name: componentName, type: \"regular\" });\n      }\n    }\n    let vms = [];\n    if (currentElement.__vms__ && Array.isArray(currentElement.__vms__)) {\n      vms = currentElement.__vms__;\n    } else if (currentElement.__vue__) {\n      vms = [currentElement.__vue__];\n    }\n    for (const vm of vms) {\n      if (!vm || !vm.$options) continue;\n      let name = vm.$options.name || vm.$options.__file || vm.$options._componentTag || \"AnonymousComponent\";\n      if (name && typeof name === \"string\" && name.includes(\"/\")) {\n        name = (String(name).split(\"/\").pop() || \"\").replace(/\\.vue$/, \"\");\n      }\n      if (!components.some((c) => c.name === name)) {\n        components.push({ name, type: \"regular\" });\n      }\n    }\n    currentElement = currentElement.parentElement;\n  }\n  if (components.length === 0 && !hasWarnedNoVue) {\n    console.warn(\n      \"[stagewise/vue] No Vue installation detected on the selected element. Make sure you are running in development mode and Vue is available.\"\n    );\n    hasWarnedNoVue = true;\n  }\n  return components.length > 0 ? components : null;\n}\nfunction getSelectedElementAnnotation(element) {\n  const hierarchy = getVueComponentHierarchy(element);\n  if (hierarchy == null ? void 0 : hierarchy[0]) {\n    return {\n      annotation: `${hierarchy[0].name}`\n    };\n  }\n  return { annotation: null };\n}\nfunction getSelectedElementsPrompt(elements) {\n  const selectedComponentHierarchies = elements.map(\n    (e) => getVueComponentHierarchy(e) || []\n  );\n  if (selectedComponentHierarchies.some((h) => h.length > 0)) {\n    const content = `This is additional information on the elements that the user selected. Use this information to find the correct element in the codebase.\n\n  ${selectedComponentHierarchies.map((h, index) => {\n      return `\n<element index=\"${index + 1}\">\n  ${h.length === 0 ? \"No Vue component as parent detected\" : `Vue component tree (from closest to farthest, 3 closest elements): ${h.map((c) => `{name: ${c.name}, type: ${c.type}}`).join(\" child of \")}`}\n</element>\n    `;\n    })}\n  `;\n    return content;\n  }\n  return null;\n}\nconst VuePlugin = {\n  displayName: \"Vue\",\n  description: \"This toolbar adds additional information and metadata for apps using Vue as an UI framework\",\n  iconSvg: /* @__PURE__ */ jsx(VueLogo, {}),\n  pluginName: \"vue\",\n  onContextElementHover: getSelectedElementAnnotation,\n  onContextElementSelect: getSelectedElementAnnotation,\n  onPromptSend: (prompt) => ({\n    contextSnippets: [\n      {\n        promptContextName: \"elements-vue-component-info\",\n        content: getSelectedElementsPrompt(prompt.contextElements)\n      }\n    ]\n  })\n};\nexport {\n  VuePlugin\n};\n"], "mappings": ";;;;;;AACA,SAAS,UAAU;AACjB,SAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,SAAS;AAAA,MACT,MAAM;AAAA,MACN,UAAU;AAAA,QACQ,EAAI,SAAS,EAAE,UAAU,WAAW,CAAC;AAAA,QACrC;AAAA,UACd;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,WAAW;AAAA,YACX,UAAU;AAAA,cACQ,EAAI,KAAK,EAAE,WAAW,4BAA4B,aAAa,OAAO,UAA0B,EAAI,QAAQ,EAAE,GAAG,sEAAsE,CAAC,EAAE,CAAC;AAAA,cAC3L,EAAI,KAAK,EAAE,WAAW,4BAA4B,gBAAgB,KAAK,UAA0B,EAAI,QAAQ,EAAE,GAAG,wEAAwE,CAAC,EAAE,CAAC;AAAA,YAChN;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,iBAAiB;AACrB,SAAS,yBAAyB,SAAS;AACzC,MAAI;AACJ,MAAI,CAAC,SAAS;AACZ,WAAO;AAAA,EACT;AACA,QAAM,aAAa,CAAC;AACpB,QAAM,gBAAgB;AACtB,MAAI,iBAAiB;AACrB,SAAO,kBAAkB,WAAW,SAAS,eAAe;AAC1D,UAAM,kBAAkB,eAAe;AACvC,SAAK,KAAK,mBAAmB,OAAO,SAAS,gBAAgB,SAAS,OAAO,SAAS,GAAG,QAAQ;AAC/F,YAAM,gBAAgB,gBAAgB,KAAK;AAC3C,UAAI,CAAC,WAAW,KAAK,CAAC,MAAM,EAAE,SAAS,aAAa,GAAG;AACrD,mBAAW,KAAK,EAAE,MAAM,eAAe,MAAM,UAAU,CAAC;AAAA,MAC1D;AAAA,IACF;AACA,QAAI,MAAM,CAAC;AACX,QAAI,eAAe,WAAW,MAAM,QAAQ,eAAe,OAAO,GAAG;AACnE,YAAM,eAAe;AAAA,IACvB,WAAW,eAAe,SAAS;AACjC,YAAM,CAAC,eAAe,OAAO;AAAA,IAC/B;AACA,eAAW,MAAM,KAAK;AACpB,UAAI,CAAC,MAAM,CAAC,GAAG;AAAU;AACzB,UAAI,OAAO,GAAG,SAAS,QAAQ,GAAG,SAAS,UAAU,GAAG,SAAS,iBAAiB;AAClF,UAAI,QAAQ,OAAO,SAAS,YAAY,KAAK,SAAS,GAAG,GAAG;AAC1D,gBAAQ,OAAO,IAAI,EAAE,MAAM,GAAG,EAAE,IAAI,KAAK,IAAI,QAAQ,UAAU,EAAE;AAAA,MACnE;AACA,UAAI,CAAC,WAAW,KAAK,CAAC,MAAM,EAAE,SAAS,IAAI,GAAG;AAC5C,mBAAW,KAAK,EAAE,MAAM,MAAM,UAAU,CAAC;AAAA,MAC3C;AAAA,IACF;AACA,qBAAiB,eAAe;AAAA,EAClC;AACA,MAAI,WAAW,WAAW,KAAK,CAAC,gBAAgB;AAC9C,YAAQ;AAAA,MACN;AAAA,IACF;AACA,qBAAiB;AAAA,EACnB;AACA,SAAO,WAAW,SAAS,IAAI,aAAa;AAC9C;AACA,SAAS,6BAA6B,SAAS;AAC7C,QAAM,YAAY,yBAAyB,OAAO;AAClD,MAAI,aAAa,OAAO,SAAS,UAAU,CAAC,GAAG;AAC7C,WAAO;AAAA,MACL,YAAY,GAAG,UAAU,CAAC,EAAE,IAAI;AAAA,IAClC;AAAA,EACF;AACA,SAAO,EAAE,YAAY,KAAK;AAC5B;AACA,SAAS,0BAA0B,UAAU;AAC3C,QAAM,+BAA+B,SAAS;AAAA,IAC5C,CAAC,MAAM,yBAAyB,CAAC,KAAK,CAAC;AAAA,EACzC;AACA,MAAI,6BAA6B,KAAK,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG;AAC1D,UAAM,UAAU;AAAA;AAAA,IAEhB,6BAA6B,IAAI,CAAC,GAAG,UAAU;AAC7C,aAAO;AAAA,kBACK,QAAQ,CAAC;AAAA,IACvB,EAAE,WAAW,IAAI,wCAAwC,sEAAsE,EAAE,IAAI,CAAC,MAAM,UAAU,EAAE,IAAI,WAAW,EAAE,IAAI,GAAG,EAAE,KAAK,YAAY,CAAC,EAAE;AAAA;AAAA;AAAA,IAGtM,CAAC,CAAC;AAAA;AAEF,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,IAAM,YAAY;AAAA,EAChB,aAAa;AAAA,EACb,aAAa;AAAA,EACb,SAAyB,EAAI,SAAS,CAAC,CAAC;AAAA,EACxC,YAAY;AAAA,EACZ,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,cAAc,CAAC,YAAY;AAAA,IACzB,iBAAiB;AAAA,MACf;AAAA,QACE,mBAAmB;AAAA,QACnB,SAAS,0BAA0B,OAAO,eAAe;AAAA,MAC3D;AAAA,IACF;AAAA,EACF;AACF;", "names": []}
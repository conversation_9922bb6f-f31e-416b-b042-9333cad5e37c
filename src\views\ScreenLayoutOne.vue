<template>
  <div class="screen-container" ref="screenContainer">
    <div class="content-container">
      <!-- 系统标题和当前时间 -->
      <div class="header-section">
        <h1 class="system-title">办案中心值班管理系统</h1>
        <div class="current-time">{{ currentTime }}</div>
      </div>

      <!-- 当前值班信息卡片 -->
      <div class="duty-cards-container">
        <!-- 新华办案中心卡片 -->
        <div class="duty-card xinhua-card">
          <div class="card-header xinhua-header">
            <h2 class="center-name">新华办案中心</h2>
          </div>
          <div class="card-content">
            <div class="duty-group-info">
              <span class="group-label">第{{ currentXinhuaDutyGroup }}组值班</span>
            </div>
            <div class="duty-leader">
              <span class="leader-label">带班民警：</span>
              <span class="leader-name">{{ currentXinhuaDutyLeader }}</span>
            </div>
            <div class="duty-phone">
              <span class="phone-label">值班电话：</span>
              <span class="phone-number" v-html="currentXinhuaDutyPhone"></span>
            </div>
          </div>
        </div>

        <!-- 长安办案中心卡片 -->
        <div class="duty-card changan-card">
          <div class="card-header changan-header">
            <h2 class="center-name">长安办案中心</h2>
          </div>
          <div class="card-content">
            <div class="duty-group-info">
              <span class="group-label">第{{ currentChanganDutyGroup }}组值班</span>
            </div>
            <div class="duty-leader">
              <span class="leader-label">带班民警：</span>
              <span class="leader-name">{{ currentChanganDutyLeader }}</span>
            </div>
            <div class="duty-phone">
              <span class="phone-label">值班电话：</span>
              <span class="phone-number" v-html="currentChanganDutyPhone"></span>
            </div>
          </div>
        </div>
      </div>

      <!-- 最后更新时间 -->
      <div class="footer-section">
        <div class="update-time">最后更新时间：{{ currentTime }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';

// 时间显示与当前日期处理
const currentTime = ref(formatTime(new Date()));
let timer;

function formatTime(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

// 更新时间
function updateTime() {
  currentTime.value = formatTime(new Date());
}

// 获取当前日期信息
const today = new Date();

// 创建一个通用函数计算值班组
function calculateDutyGroup(date, startDate, startGroup, totalGroups) {
  // 计算从起始日期到目标日期的天数差
  const targetDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
  const baseDate = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());
  const diffTime = targetDate.getTime() - baseDate.getTime();
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
  
  // 基于天数差和起始组计算当前值班组
  // 例如：如果有4个组，起始组是2，则按照2->3->4->1->2->...的顺序循环
  const groupOffset = diffDays % totalGroups;
  let dutyGroup = (startGroup + groupOffset) % totalGroups;
  if (dutyGroup === 0) dutyGroup = totalGroups; // 如果余数为0，说明是最后一组
  
  return dutyGroup;
}

// 定义起始日期和组的配置
const xinhuaStartDate = new Date(2025, 6, 1); // 2025年7月1日
const xinhuaStartGroup = 2;  // 新华7月1日是2组值班
const totalGroups = 4;  // 总共4个组

const changanStartDate = new Date(2025, 6, 1); // 2025年7月1日
const changanStartGroup = 3; // 长安7月1日是3组值班

// 计算当前新华值班组
const currentXinhuaDutyGroup = computed(() => {
  return calculateDutyGroup(today, xinhuaStartDate, xinhuaStartGroup, totalGroups);
});

// 计算当前长安值班组
const currentChanganDutyGroup = computed(() => {
  return calculateDutyGroup(today, changanStartDate, changanStartGroup, totalGroups);
});

// 获取当前新华值班组的带班民警和电话
const currentXinhuaDutyLeader = computed(() => {
  const leaders = {
    1: "张英",
    2: "池海涛/叶晓翼",
    3: "刘志华/张海英",
    4: "赵伟力/梁华"
  };
  return leaders[currentXinhuaDutyGroup.value] || "";
});

const currentXinhuaDutyPhone = computed(() => {
  const phones = {
    1: "682323",
    2: "663052",
    3: "662430",
    4: "662407"
  };
  const phonesSecond = {
    1: "",  // 移除李蕊的电话
    2: "662929",
    3: "662930",
    4: "631851"
  };
  
  // 如果是第一组且没有第二个电话，就不显示分隔符
  if (currentXinhuaDutyGroup.value === 1) {
    return phones[1];
  }
  
  return `${phones[currentXinhuaDutyGroup.value] || ""}<span class="phone-separator">|</span>${phonesSecond[currentXinhuaDutyGroup.value] || ""}`;
});

// 获取当前长安值班组的带班民警和电话
const currentChanganDutyLeader = computed(() => {
  const leaders = {
    1: "李建财/张振峰",
    2: "侯新宇/夏云龙",
    3: "成峰林/解兵海",
    4: "曹聚刚/杨国伟"
  };
  return leaders[currentChanganDutyGroup.value] || "";
});

const currentChanganDutyPhone = computed(() => {
  const phones = {
    1: "669302",
    2: "662713",
    3: "680205",
    4: "669695"
  };
  const phonesSecond = {
    1: "665507",
    2: "669574",
    3: "662963",
    4: "669694"
  };
  return `${phones[currentChanganDutyGroup.value] || ""}<span class="phone-separator">|</span>${phonesSecond[currentChanganDutyGroup.value] || ""}`;
});



// 全屏相关
const screenContainer = ref(null);
const isFullscreen = ref(false);

// 切换全屏模式
async function toggleFullscreen() {
  if (!document.fullscreenElement) {
    try {
      await screenContainer.value.requestFullscreen();
      isFullscreen.value = true;
    } catch (err) {
      console.error('无法进入全屏模式：', err);
    }
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen();
      isFullscreen.value = false;
    }
  }
}

onMounted(() => {
  timer = setInterval(updateTime, 1000);
  
  // 尝试自动进入全屏模式
  const enterFullscreen = async () => {
    try {
      if (screenContainer.value && !document.fullscreenElement) {
        await screenContainer.value.requestFullscreen();
        isFullscreen.value = true;
    }
    } catch (err) {
      console.error('自动进入全屏模式失败：', err);
    }
  };
  
  // 延迟1秒后尝试进入全屏模式
  setTimeout(enterFullscreen, 1000);
  
  // 监听键盘事件，F11全屏切换
  const handleKeyDown = (event) => {
    if (event.key === 'F11') {
      event.preventDefault();
      toggleFullscreen();
    }
  };
  
  document.addEventListener('keydown', handleKeyDown);
});

onUnmounted(() => {
  clearInterval(timer);
  // 退出全屏
  if (document.fullscreenElement) {
    document.exitFullscreen();
  }
});
</script>

<style scoped>
.screen-container {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "Helvetica Neue", Helvetica, "PingFang SC", "Microsoft YaHei", sans-serif;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  letter-spacing: -0.02em;
}

.content-container {
  display: flex;
  flex: 1;
  flex-direction: column;
  padding: clamp(1rem, 2vw, 3rem);
  gap: clamp(1rem, 2vh, 2rem);
  overflow: hidden;
  min-height: 0;
}

/* 系统标题和时间 */
.header-section {
  text-align: center;
  margin-bottom: clamp(1rem, 2vh, 2rem);
}

.system-title {
  font-size: clamp(4rem, 8vw, 12rem);
  font-weight: 600;
  margin: 0 0 clamp(0.5rem, 1vh, 1rem) 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.current-time {
  font-size: clamp(2rem, 4vw, 3.5rem);
  opacity: 0.9;
  font-weight: 400;
}

/* 值班卡片容器 */
.duty-cards-container {
  display: flex;
  flex: 1;
  gap: clamp(1rem, 3vw, 3rem);
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

/* 值班卡片 */
.duty-card {
  flex: 1;
  min-width: 300px;
  max-width: 600px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.duty-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
}

/* 卡片头部 */
.card-header {
  padding: clamp(1.5rem, 3vh, 3rem);
  text-align: center;
  color: white;
  font-weight: 600;
}

.xinhua-header {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  border-top: 4px solid #2E7D32;
}

.changan-header {
  background: linear-gradient(135deg, #2196F3, #1976D2);
  border-top: 4px solid #0D47A1;
}

.center-name {
  font-size: clamp(3rem, 5vw, 6rem);
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* 卡片内容 */
.card-content {
  padding: clamp(2rem, 4vh, 4rem);
  color: #333;
}

.duty-group-info {
  text-align: center;
  margin-bottom: clamp(2rem, 3vh, 3rem);
}

.group-label {
  font-size: clamp(2.5rem, 4.5vw, 4rem);
  font-weight: 600;
  color: #666;
  background: linear-gradient(135deg, #f0f0f0, #e0e0e0);
  padding: clamp(1rem, 2vh, 2rem) clamp(2rem, 4vw, 4rem);
  border-radius: 12px;
  display: inline-block;
}

.duty-leader, .duty-phone {
  margin-bottom: clamp(1.5rem, 3vh, 2.5rem);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: clamp(1rem, 2vw, 2rem);
}

.leader-label, .phone-label {
  font-size: clamp(2.2rem, 4vw, 3.5rem);
  font-weight: 500;
  color: #666;
  white-space: nowrap;
}

.leader-name {
  font-size: clamp(2.5rem, 4.5vw, 4rem);
  font-weight: 600;
  color: #333;
  text-align: right;
  flex: 1;
}

.phone-number {
  font-size: clamp(3rem, 5vw, 5rem);
  font-weight: 700;
  color: #e53e3e;
  text-align: right;
  flex: 1;
  font-family: 'Courier New', monospace;
}

/* 底部更新时间 */
.footer-section {
  text-align: center;
  margin-top: auto;
  padding-top: clamp(1rem, 2vh, 2rem);
}

.update-time {
  font-size: clamp(1.5rem, 2.5vw, 2.2rem);
  opacity: 0.8;
  font-weight: 400;
}

/* 电话号码分隔符样式 */
.phone-separator {
  display: inline-block;
  color: rgba(229, 62, 62, 0.6);
  font-weight: 300;
  margin: 0 clamp(0.8rem, 1.5vw, 1.5rem);
  vertical-align: middle;
  font-size: clamp(2.5rem, 4.5vw, 4rem);
}

/* 响应式布局调整 */
@media screen and (max-width: 768px) {
  .duty-cards-container {
    flex-direction: column;
    gap: clamp(1rem, 2vh, 2rem);
  }

  .duty-card {
    min-width: unset;
    max-width: unset;
  }

  .duty-leader, .duty-phone {
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
  }

  .leader-name, .phone-number {
    text-align: left;
    flex: unset;
  }
}

@media screen and (min-width: 1440px) {
  .duty-cards-container {
    gap: clamp(2rem, 4vw, 4rem);
  }

  .system-title {
    font-size: clamp(3rem, 5vw, 5rem);
  }

  .current-time {
    font-size: clamp(1.2rem, 2.5vw, 1.8rem);
  }

  .center-name {
    font-size: clamp(1.8rem, 3vw, 2.8rem);
  }

  .group-label {
    font-size: clamp(1.4rem, 2.5vw, 2.2rem);
  }

  .leader-label, .phone-label {
    font-size: clamp(1.2rem, 2.2vw, 1.8rem);
  }

  .leader-name {
    font-size: clamp(1.4rem, 2.5vw, 2rem);
  }

  .phone-number {
    font-size: clamp(1.8rem, 3vw, 2.5rem);
  }
}

@media screen and (min-width: 1920px) {
  .system-title {
    font-size: clamp(4rem, 6vw, 6rem);
  }

  .current-time {
    font-size: clamp(1.4rem, 3vw, 2.2rem);
  }

  .center-name {
    font-size: clamp(2.2rem, 4vw, 3.5rem);
  }

  .group-label {
    font-size: clamp(1.8rem, 3vw, 2.8rem);
  }

  .leader-label, .phone-label {
    font-size: clamp(1.4rem, 2.8vw, 2.2rem);
  }

  .leader-name {
    font-size: clamp(1.8rem, 3vw, 2.5rem);
  }

  .phone-number {
    font-size: clamp(2.2rem, 4vw, 3.2rem);
  }

  .phone-separator {
    font-size: clamp(1.8rem, 3vw, 2.5rem);
  }
}

/* 超大屏幕适配 */
@media screen and (min-width: 2560px) {
  .content-container {
    padding: clamp(2rem, 3vw, 4rem);
  }

  .duty-card {
    padding: clamp(1rem, 2vw, 2rem);
  }

  .card-header {
    padding: clamp(2rem, 3vh, 3rem);
  }

  .card-content {
    padding: clamp(2rem, 4vh, 4rem);
  }
}






</style> 
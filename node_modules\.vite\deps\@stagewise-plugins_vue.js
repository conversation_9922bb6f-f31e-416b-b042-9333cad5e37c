import {
  u
} from "./chunk-BCKZVBEU.js";
import "./chunk-5WWUZCGV.js";

// node_modules/@stagewise-plugins/vue/dist/index.es.js
function VueLogo() {
  return u(
    "svg",
    {
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 261.76 226.69",
      fill: "currentColor",
      children: [
        u("title", { children: "Vue Logo" }),
        u(
          "g",
          {
            xmlns: "http://www.w3.org/2000/svg",
            transform: "matrix(1.3333 0 0 -1.3333 -76.311 313.34)",
            children: [
              u("g", { transform: "translate(178.06 235.01)", fillOpacity: "0.5", children: u("path", { d: "m0 0-22.669-39.264-22.669 39.264h-75.491l98.16-170.02 98.16 170.02z" }) }),
              u("g", { transform: "translate(178.06 235.01)", "fill-opacity": "1", children: u("path", { d: "m0 0-22.669-39.264-22.669 39.264h-36.227l58.896-102.01 58.896 102.01z" }) })
            ]
          }
        )
      ]
    }
  );
}
var hasWarnedNoVue = false;
function getVueComponentHierarchy(element) {
  var _a;
  if (!element) {
    return null;
  }
  const components = [];
  const maxComponents = 3;
  let currentElement = element;
  while (currentElement && components.length < maxComponents) {
    const parentComponent = currentElement.__vueParentComponent;
    if ((_a = parentComponent == null ? void 0 : parentComponent.type) == null ? void 0 : _a.__name) {
      const componentName = parentComponent.type.__name;
      if (!components.some((c) => c.name === componentName)) {
        components.push({ name: componentName, type: "regular" });
      }
    }
    let vms = [];
    if (currentElement.__vms__ && Array.isArray(currentElement.__vms__)) {
      vms = currentElement.__vms__;
    } else if (currentElement.__vue__) {
      vms = [currentElement.__vue__];
    }
    for (const vm of vms) {
      if (!vm || !vm.$options)
        continue;
      let name = vm.$options.name || vm.$options.__file || vm.$options._componentTag || "AnonymousComponent";
      if (name && typeof name === "string" && name.includes("/")) {
        name = (String(name).split("/").pop() || "").replace(/\.vue$/, "");
      }
      if (!components.some((c) => c.name === name)) {
        components.push({ name, type: "regular" });
      }
    }
    currentElement = currentElement.parentElement;
  }
  if (components.length === 0 && !hasWarnedNoVue) {
    console.warn(
      "[stagewise/vue] No Vue installation detected on the selected element. Make sure you are running in development mode and Vue is available."
    );
    hasWarnedNoVue = true;
  }
  return components.length > 0 ? components : null;
}
function getSelectedElementAnnotation(element) {
  const hierarchy = getVueComponentHierarchy(element);
  if (hierarchy == null ? void 0 : hierarchy[0]) {
    return {
      annotation: `${hierarchy[0].name}`
    };
  }
  return { annotation: null };
}
function getSelectedElementsPrompt(elements) {
  const selectedComponentHierarchies = elements.map(
    (e) => getVueComponentHierarchy(e) || []
  );
  if (selectedComponentHierarchies.some((h) => h.length > 0)) {
    const content = `This is additional information on the elements that the user selected. Use this information to find the correct element in the codebase.

  ${selectedComponentHierarchies.map((h, index) => {
      return `
<element index="${index + 1}">
  ${h.length === 0 ? "No Vue component as parent detected" : `Vue component tree (from closest to farthest, 3 closest elements): ${h.map((c) => `{name: ${c.name}, type: ${c.type}}`).join(" child of ")}`}
</element>
    `;
    })}
  `;
    return content;
  }
  return null;
}
var VuePlugin = {
  displayName: "Vue",
  description: "This toolbar adds additional information and metadata for apps using Vue as an UI framework",
  iconSvg: u(VueLogo, {}),
  pluginName: "vue",
  onContextElementHover: getSelectedElementAnnotation,
  onContextElementSelect: getSelectedElementAnnotation,
  onPromptSend: (prompt) => ({
    contextSnippets: [
      {
        promptContextName: "elements-vue-component-info",
        content: getSelectedElementsPrompt(prompt.contextElements)
      }
    ]
  })
};
export {
  VuePlugin
};
//# sourceMappingURL=@stagewise-plugins_vue.js.map
